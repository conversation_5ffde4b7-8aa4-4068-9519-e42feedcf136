<?php
/*
 * Template Name: Recruit Details Template
 * Template Post Type: post
 */

use Soap\Url;

?>
<style>
	.wrapper .header-bg {
		margin: 10px 0;
		position: relative;
		width: 100%;
		height: 350px;
	}
	.wrapper .header-bg img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
	.wrapper .header-bg .text {
		display: flex;
		flex-direction: column;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
	}
	.wrapper .header-bg .text-h1{
		position: relative;
        margin: 0 auto;
		margin-bottom: 15px;
		padding: 0 6px 0 6px;
		color: #fff;
		font-size: 48px;
		width: fit-content;
		border-radius: 5px;
		text-align: center;
		font-family: 'yumindb';
	}
	.wrapper .header-bg .text-h1::before{
		position: absolute;
		content: '';
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		background-color: #01A369;
		opacity: 0.6;
		z-index: -1;
		border-radius: 5px;
	}
	.wrapper .header-bg .text-h2{
		position: relative;
		padding: 3px 12px 3px 12px;
		color: #fff;
		border-radius: 5px;
		font-size: 48px;
		font-weight: bold;
		font-family: 'yumindb';
	}
	.wrapper .header-bg .text-h2::before{
		position: absolute;
		content: '';
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		background-color: #01A369;
		opacity: 0.6;
		z-index: -1;
	}
	.content{
		max-width: 720px !important;
		margin: 50px auto 0 auto !important;
		padding: 10px 30px;
		border-radius: 10px;
		box-shadow: 0px 0px 2px 2px #e3e3e3;
		border-top: 8px solid;
	}	
	.content.cl-green{
		border-color: #01A369;
	}
	.content.cl-brown{
		border-color: #804B39;
	}
	.content.cl-yellow{
		border-color: #DEDB6B;
	}
	.content.cl-black{
		border-color: #23272E;
	}
	.content .post-title{
		display: flex;
		margin-top: 20px;
		padding-bottom: 20px;
		justify-content: space-between;
		font-weight: bold;
		font-size: 18px;
		color: #01A369;
	}
	.content .recruit-details{
		padding-bottom: 30px;
		text-align: left;
	}
	.content .recruit-details .bl_line {
		margin: 35px 0;
		height: 1px;
		border-bottom: 1px dashed green;
	}
	.content .recruit-details .bl_lv1{
		position: relative;
	}
	.content .recruit-details .bl_lv2{
		position: relative;
		margin-left: 20px;
	}
	.content .recruit-details .bl_lv1::before {
		position: absolute;
		content: '';
		left: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 10px;
		height: 10px;
		background-color: #01A369;
		border-radius: 50%;
	}
	.content .recruit-details .bl_lv2::before {
		position: absolute;
		content: '';
		left: 0;
		top: 50%;
		transform: translateY(-50%) rotate(45deg);
		width: 6px;
		height: 6px;
		border-top: 3px solid #01A369;
		border-right: 3px solid #01A369;
	}
	.content .recruit-details .wp-block-table{
		width: 100%;
	}
	.content .recruit-details .wp-block-table td, th{
		padding: 2px 6px;
		border: 1px solid;
	}
	.content .recruit-details .wp-block-table tr td:first-child{
		width: 35%;
	}
	.content .recruit-details .wp-block-table tr td:last-child {
		width: 65%;
	}
	.content .navigation{
		display: flex;
		margin: 25px 0 55px 0;
		justify-content: center;
	}
	.content .navigation .btn{
		display: flex;
		padding: 6px 12px;
		border-radius: 24px;
		font-weight: bold;
		align-items: center;
		cursor: pointer;
		font-size: 16px;
	}
	.content .navigation .btn span{
		display: flex;
		align-items: center;
		justify-content: center;
		width: 30px;
		height: 30px;
		border-radius: 50%;
		border: 1px solid #01A369;
		background-color: #fff;
	}
	.content .navigation .btn svg{
		width: 18px;
		height: 18px;
		fill: currentColor;
		color: #01A369;
	}
	.content .navigation .btn-back{
		margin-right: 15px;
		width: 120px;
		border: 1px solid #01A369;
		color: #01A369;
	}
	.content .navigation .btn-back span{
		margin-right: 24px;
	}
	.content .navigation .btn-approve{
		border: unset;
		justify-content: end;
		width: 220px;
		background-color: #01A369;
		color: #fff;
	}
	.content .navigation .btn-approve span{
		margin-left: 40px;
	}
	.wrapper .process{
		padding: 35px 0 100px 0;
	}
	.wrapper .process h2{
		font-size: 24px;
		padding: 5px 10px 25px 5px;
		text-align: center;
		font-weight: bold;
		color: #01A369;
	}
	.wrapper .process .list-process{
		display: flex;
		justify-content: center;
	}
	.wrapper .process .list-process .item{
		margin: 0 10px;
		width: 170;
		height: 145px;
	}
	.wrapper .process .list-process .item .text{
		padding-right: 15px;
		color: #01A369;
		font-weight: bold;
		text-align: center;
	}
	.wrapper .process .list-process .item .text.cl-green{
		padding-right: 20px;
		color: #01A369;
	}
	.wrapper .process .list-process .item .text.cl-brown{
		color: #804B39;
	}
	.wrapper .process .list-process .item .text.cl-yellow{
		color: #DEDB6B;
	}
	.wrapper .process .list-process .item .text.cl-black{
		padding-right: 15px;
		color: #23272E;
	}

	/* @media screen and (max-width: 767px) {
		.process {
			display: none;
		}
	} */
</style>
<?php get_header(); ?>
<div class="wrapper">
	<div class="header-bg">
		<img src="/wp-content/uploads/2024/09/hero-image.jpg" alt="">
		<div class="text">
			<div class="text-h1">経験者採用</div>
			<div class="text-h2">次のステージはここにある</div>
		</div>
	</div>
	<?php 
		$catagory = get_the_category();
	?>
	<div class="content cl-<?php echo $catagory[0]->slug; ?>">
		<!-- <div class="title-recuit">
			<h2>キャリア採用募集要項</h2>
		</div> -->
		<?php
		if ( have_posts() ) : 
			while ( have_posts() ) : the_post();
			?>
			<div class="post-title">
			<?php
				
				// the_category('', '');
				the_title( '<div class="title-text">', '</div>' );
				// the_date('Y-m-d', '<div class="create-date">掲載日: ', '</div>');
			?>
			</div>
			<div class="recruit-details">
			<?php
				the_content();
			endwhile;
		endif;
		?>
		</div>
		<div class="process <?php if Url>">
			<h2>選考プロセスs</h2>
			<div class="list-process">
				<div class="item">
					<div class="image">
						<img src="/wp-content/uploads/2024/09/process-step1.png" alt="">
					</div>
					<div class="text cl-green">応募</div>
				</div>
				<div class="item">
					<div class="image">
						<img src="/wp-content/uploads/2024/09/process-step2.png" alt="">
					</div>
					<div class="text cl-brown">書類選考 <br>（合格者にのみ連絡）</div>
				</div>
				<div class="item">
					<div class="image">
						<img src="/wp-content/uploads/2024/09/process-step3.png" alt="">
					</div>
					<div class="text cl-yellow">面接（複数回）、<br>筆記試験　等</div>
				</div>
				<div class="item">
					<div class="image">
						<img src="/wp-content/uploads/2024/09/process-step4.png" alt="">
					</div>
					<div class="text cl-black">内定</div>
				</div>
			</div>
		</div>
		<div class="navigation">
			<div class="btn btn-back" onClick="history.back();">
				<span>
					<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
						<path d="M9.4 233.4c-12.5 12.5-12.5 32.8 0 45.3l160 160c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L109.2 288 416 288c17.7 0 32-14.3 32-32s-14.3-32-32-32l-306.7 0L214.6 118.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-160 160z"/>
					</svg>
				</span>	
				戻る
			</div>
			
			<div>
				<form action="/recruit/recruit-form.html" method="post">
					<input type="hidden" name="job_name" value="<?php the_title();?>">
					<input type="hidden" name="job_color" value="cl-<?php echo $catagory[0]->slug; ?>">
					<button type="submit" class="btn btn-approve">
						応募する
						<span>
							<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
								<path d="M438.6 278.6c12.5-12.5 12.5-32.8 0-45.3l-160-160c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L338.8 224 32 224c-17.7 0-32 14.3-32 32s14.3 32 32 32l306.7 0L233.4 393.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l160-160z"/>
							</svg>
						</span>
					</button>
				</form>
			</div>
			
		</div>
	</div>
	
</div><!-- div class="wrapper" -->
<?php get_footer(); ?>
